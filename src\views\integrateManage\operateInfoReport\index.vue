<template>
  <div class="operate-info-container micro-app-sctmp_base">
    <header class="header">
      <h2 class="header-title">运营信息报表</h2>
      <el-tabs class="header-tabs" v-model="activeTab">
        <el-tab-pane :label="item" v-for="(item, index) in tabList" :key="index"></el-tab-pane>
      </el-tabs>
    </header>
    <main class="main">
      <Title title="垃圾收运量信息"></Title>
      <div class="rubbish-box">
        <div class="rubbish-header">
          <SubTitle title="今日垃圾收运量"></SubTitle>
          <TimeScreen></TimeScreen>
        </div>
        <div class="rubbish-grid-box">
          <div class="rubbish-content">
            <div class="rubbish-content-title">收运垃圾总重量</div>
            <div class="rubbish-content-box">
              <div class="rubbish-count">
                <div class="count-big">3577998</div>
                <div class="count-unit">（kg）</div>
              </div>
            </div>
          </div>
          <div>
            <el-row :gutter="16">
              <el-col :lg="12" :xl="8" v-for="(item, index) in rubbishList.slice(0, 3)" :key="index">
                <div class="rubbish-content">
                  <div class="rubbish-content-title">{{ item }}</div>
                  <div class="rubbish-content-box">
                    <div class="rubbish-count">
                      <div class="count-small">3577998</div>
                      <div class="count-unit">（kg）</div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="16" class="mt-16">
              <el-col :lg="12" :xl="8" v-for="(item, index) in rubbishList.slice(3, 6)" :key="index">
                <div class="rubbish-content">
                  <div class="rubbish-content-title">{{ item }}</div>
                  <div class="rubbish-content-box">
                    <div class="rubbish-count">
                      <div class="count-small">3577998</div>
                      <div class="count-unit">（kg）</div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
  import Title from "./components/Title.vue";
  import SubTitle from "./components/SubTitle.vue";
  import TimeScreen from "./components/TimeScreen.vue";
  export default {
    components: {
      Title,
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        activeTab: 0,
        tabList: ["运输管理信息"],
        rubbishList: [
          "感染性废物重量",
          "损伤性废物重量",
          "药物性废物重量",
          "病理性废物重量",
          "化学性废物重量",
          "感染性——污泥重量",
        ],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .operate-info-container {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
  }
  .header {
    background-color: #fff;
    padding: 20px 30px 0 30px;
    .header-title {
      text-align: center;
      margin-top: 0;
    }
    ::v-deep .header-tabs .el-tabs__header {
      margin: 0;
    }
  }
  .main {
    padding: 0 30px;
  }
  .rubbish-box {
    padding: 16px;
    background-color: #fff;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    .rubbish-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }
    .rubbish-grid-box {
      display: grid;
      grid-gap: 16px;
      grid-template-columns: repeat(2, 1fr);
      .rubbish-content {
        padding: 16px;
        border: 1px solid #d7d7d7;
        border-radius: 6px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        .rubbish-content-title {
          font-size: 14px;
          color: #333;
        }
        .rubbish-content-box {
          flex: 1;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 16px;
        }
        .rubbish-count {
          display: flex;
          align-items: flex-end;
          .count-big {
            font-size: 50px;
            font-weight: bold;
            line-height: 38px;
          }
          .count-small {
            font-size: 28px;
            line-height: 24px;
          }
          .count-unit {
            font-size: 12px;
          }
        }
      }
    }
  }
  .mt-16 {
    margin-top: 16px;
  }
</style>
