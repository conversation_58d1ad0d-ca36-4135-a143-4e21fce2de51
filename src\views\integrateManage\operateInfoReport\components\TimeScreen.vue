<template>
  <div class="flex-h micro-app-sctmp_base">
    <div class="mr-10">
      <el-date-picker
        v-model="timeForm.day"
        type="date"
        placeholder="请选择日期"
        value-format="yyyy-MM-dd"
        :clearable="false"
        v-if="radioValue === 0"
      ></el-date-picker>
      <el-date-picker
        v-model="timeForm.month"
        type="month"
        placeholder="请选择月份"
        :clearable="false"
        v-else-if="radioValue == 1"
      ></el-date-picker>
      <el-date-picker
        v-model="timeForm.year"
        type="year"
        placeholder="请选择年份"
        :clearable="false"
        v-else
      ></el-date-picker>
    </div>
    <el-radio-group v-model="radioValue" size="medium">
      <el-radio-button :label="0">日</el-radio-button>
      <el-radio-button :label="1">月</el-radio-button>
      <el-radio-button :label="2">季</el-radio-button>
      <el-radio-button :label="3">年</el-radio-button>
    </el-radio-group>
  </div>
</template>

<script>
  import moment from "moment";
  export default {
    data() {
      return {
        radioValue: 0,
        timeForm: {
          year: moment().format("YYYY"),
          month: moment().format("YYYY-MM"),
          day: moment().format("YYYY-MM-DD"),
          quarter: "",
        },
      };
    },
    created() {
      console.log("this.timeForm ==> ", this.timeForm);
    },
    mounted() {},
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .mr-10 {
    margin-right: 10px;
  }
  ::v-deep .el-radio-button__inner:hover {
    color: #003366;
  }
  ::v-deep .is-active .el-radio-button__inner:hover {
    color: #fff;
  }
  ::v-deep .is-active .el-radio-button__inner {
    background-color: #003366;
    border-color: #003366;
  }
</style>
